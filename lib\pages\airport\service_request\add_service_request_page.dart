import 'dart:convert';
import 'dart:io';

import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/bloc/part_items/selected_part_item_bloc.dart';
import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/cubit/location/location_cubit.dart';
import 'package:alink/cubit/pages/repair_detail/repair_button_cubit.dart';
import 'package:alink/data/model/barcode_response.dart';
import 'package:alink/data/model/choice.dart';
import 'package:alink/data/model/customization.dart';
import 'package:alink/data/model/drop_down_option.dart';
import 'package:alink/data/model/extension.dart';
import 'package:alink/data/model/location_detail.dart';
import 'package:alink/data/model/part_item_response.dart';
import 'package:alink/database/database.dart';
import 'package:alink/pages/airport/repair/search_item_part_page.dart';
import 'package:alink/pages/airport/service_request/widget/location_label.dart';
import 'package:alink/pages/auth/login_page.dart';
import 'package:alink/pages/dashboard_page.dart';
import 'package:alink/pages/full_image_view.dart';
import 'package:alink/pages/image_editor/edit_image_home_page.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/app_constant.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/util/enums/app_enum.dart';
import 'package:alink/widget/location_widget.dart';
import 'package:alink/widget/seperator.dart';
import 'package:drift/drift.dart' as moor;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:location/location.dart' as gps;
import 'package:provider/provider.dart';

import 'package:shared_preferences/shared_preferences.dart';

import '../../../data/model/singleton_model.dart';
import '../../../logger/logger.dart';
import '../../../provider/locationProvider.dart';

class AddServiceRequestPage extends StatefulWidget {
  static const routeName = "add-services";
  final ServiceType? serviceRequestData;

  const AddServiceRequestPage({Key? key, this.serviceRequestData})
      : super(key: key);

  @override
  _AddServiceRequestPageState createState() => _AddServiceRequestPageState();
}

class _AddServiceRequestPageState extends State<AddServiceRequestPage>
    with TickerProviderStateMixin {
  static const String className = '_AddServiceRequestPageState';

  // Default Radio Button Item
  Choice? radioItem;

  // Group Value for Radio Button.
  String? choiceValue;
  String? subChoiceValue;

  List<Map<String, dynamic>> selectedLocation = [];
  String selectedLocationId = '';
  String endLocationCategory = '';
  String endLocationName = '';
  List<LocationDetail> locationList = [];
  bool isLocationConfirmed = false;
  bool isLocationFreezed = true;
  late bool isFirstTime;
  bool changeTerminalControlDisabled = true;
  bool isSafetyIssue = false;
  LocationProvider? locationProvider;
  bool fetchingLocation = false;

  bool hasSubChoice = false;

  String? pickedImage;
  var imageList = [];
  late TextEditingController _descriptionTextController;
  final ImagePicker _picker = ImagePicker();
  String? base64Img;

  //TASKLIST LIST FROM SERVER
  List<Choice> taskList = [];
  List<bool> doesChoiceHasSubChoice = [];

  // EXTENSION LIST FROM SERVER
  List<Extension> extensionList = [];

  // Extension submit response map
  Map<String, dynamic> extensionMap = {};
  bool isValueNotExist = true;
  gps.LocationData? locationData;
  BarcodeResponse? barcodeResponse;
  Logger logger = Logger();
  FocusNode inputFocusNode = FocusNode();

  ServiceRequestBloc get serviceRequestBloc =>
      BlocProvider.of<ServiceRequestBloc>(context);

  @override
  void initState() {
    locationProvider = Provider.of<LocationProvider>(context, listen: false);
    isFirstTime = true;
    _initBarCodeResponse();
    _initLocation();
    super.initState();
  }

  ApiBloc get apiBloc => BlocProvider.of<ApiBloc>(context);

  LocationCubit get locationCubit => BlocProvider.of<LocationCubit>(context);

  @override
  void dispose() {
    _descriptionTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    locationProvider = Provider.of<LocationProvider>(context);
    Logger.i("Class Name: " + className);
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: WillPopScope(
        child: Scaffold(
          body: Center(
              child: Container(
            color: Colors.white,
            constraints: const BoxConstraints(maxWidth: 500),
            child: Column(
              children: [
                ApplicationUtil.displayNotificationWidgetIfExist(
                    context, AddServiceRequestPage.routeName),
                const SizedBox(
                  height: 10,
                ),
                _getFormBody()
              ],
            ),
          )),
          floatingActionButton: FloatingActionButton(
            onPressed: () {
              Logger.i("on back pressed");
              FocusManager.instance.primaryFocus?.unfocus();
              showAlertDialog(context);
            },
            child: Container(
                margin: const EdgeInsets.only(right: 5),
                child: const FaIcon(FontAwesomeIcons.chevronLeft)),
          ),
        ),
        onWillPop: () async {
          showAlertDialog(context);
          return false;
        },
      ),
    );
  }

  _getFormBody() {
    return Expanded(
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(
            parent: AlwaysScrollableScrollPhysics()),
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 10),
          child: Column(
            children: [
              Column(
                children: [
                  const SizedBox(
                    height: 10,
                  ),
                  _getSafetyIssueBox(),
                  _getLocationBox(),
                  const SizedBox(
                    height: 10,
                  ),
                ],
              ),
              _getOtherFormDetail()
            ],
          ),
        ),
      ),
    );
  }

  _getOtherFormDetail() => AnimatedSize(
        duration: const Duration(milliseconds: 300),
        //vsync: this,
        child: isLocationConfirmed
            ? Column(
                children: [
                  const SizedBox(
                    height: 10,
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 10, horizontal: 0),
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(color: Theme.of(context).primaryColor),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(
                          height: 5,
                        ),
                        //_getIsSafetyIssueCheckBox(),
                        //GET TERMINAL DETAIL
                        _getSelectedLocationDetail(),
                        const SizedBox(
                          height: 10,
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 15),
                          child: const DottedDivider(
                            color: AppColor.redColor,
                          ),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        //GET TASK FORM
                        getIt<ServiceType>().type !=
                                ServiceRequestType.MOVE_EQUIPMENT
                            ? _getTaskForms(context)
                            : Container(),
                        _getOtherForm(context),
                        const SizedBox(
                          height: 30,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                ],
              )
            : Container(),
      );

  _getImages() {
    if (imageList == null || imageList.isEmpty) {
      return _imagePlaceHolder();
    }
    return SizedBox(
      height: 90,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: imageList.length,
        itemBuilder: (context, index) {
          var base64Img = imageList[index];
          if (index == 0) {
            return Row(
              children: [
                _imagePlaceHolder(),
                _getSingleImage(base64Img, index)
              ],
            );
          } else {
            return _getSingleImage(base64Img, index);
          }
        },
      ),
    );
  }

  _getFormsButtons() => Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          BlocConsumer<ServiceRequestBloc, ServiceRequestState>(
            listener: (context, state) {
              print(state.toString());
              if (state is ServiceRequestError) {
                ApplicationUtil.showSnackBar(
                    context: context, message: state.error);
              }
              if (state is ServiceRequestSaved) {
                //ApplicationUtil.showToast(msg: 'Service Request Created');
                _clearBlocList();
                // Navigator.popAndPushNamed(context, DashboardPage.routeName);
                if (getIt<ServiceType>().type ==
                    ServiceRequestType.NOTIFICATION) {
                  sendDataToServer();
                  Navigator.pop(context, true);
                } else {
                  Navigator.pushNamedAndRemoveUntil(context,
                      DashboardPage.routeName, (Route<dynamic> route) => false);
                }
              }
            },
            builder: (context, state) {
              if (state is ServiceRequestInitial) {
                return _buildFinishButton();
              } else if (state is ServiceRequestSaving ||
                  state is ServiceRequestUpdating) {
                return _buildProgressButton();
              } else {
                return _buildFinishButton();
              }
            },
          ),
          /* ElevatedButton(
            style: ElevatedButton.styleFrom(
              primary: Color(0xff999999),
              shape: new RoundedRectangleBorder(
                borderRadius: new BorderRadius.circular(15.0),
              ),
            ),
            onPressed: () {
              Navigator.pop(context);
            },
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 12, horizontal: 15),
              child: Text(
                AppLocalizations.of(context)!.cancel,
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ),
          )*/
        ],
      );

  _getLocationBox() => Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: Theme.of(context).primaryColor),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(
              height: 10,
            ),
            Text(
              AppLocalizations.of(context)!.confirmLocation,
              style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  fontSize: 20,
                  fontWeight: FontWeight.bold),
            ),
            const SizedBox(
              height: 10,
            ),
            const DottedDivider(
              color: AppColor.redColor,
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              AppLocalizations.of(context)!.confirmLocationMessage,
              style: const TextStyle(
                color: AppColor.greyTextColor,
                fontSize: 16,
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            _getLocationDropDown(),
            const SizedBox(
              height: 10,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                _confirmLocationButton(),
                const SizedBox(
                  width: 18,
                ),
                _getChangeLocationButton(),
              ],
            )
          ],
        ),
      );

  _imagePlaceHolder() => InkWell(
        onTap: () async {
          FocusScope.of(context).requestFocus(FocusNode());
          SharedPreferences sharedPreference = getIt<SharedPreferences>();
          String type =
              sharedPreference.getString("cameraPref") ?? AppConstant.CAMERA;
          if (type == AppConstant.CAMERA) {
            _onImageButtonPressed(ImageSource.camera, context: context);
          }
        },
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 10),
          decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(15)),
          width: 85,
          height: 85,
          child: Center(
            child: FaIcon(
              FontAwesomeIcons.solidCamera,
              size: 30,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
      );

  _getSingleImage(base64imageString, int index) => SizedBox(
        height: 90,
        child: Stack(
          alignment: Alignment.center,
          children: [
            InkWell(
              onTap: () {
                FocusManager.instance.primaryFocus?.unfocus();
                print(index);
                Navigator.pushNamed(context, ImageViewPage.routeName,
                    arguments:
                        ImageWithTag(base64: base64imageString, index: index));
              },
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 5),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(10)),
                  child: Hero(
                    tag: index,
                    child: Image.memory(
                      base64Decode(base64imageString),
                      fit: BoxFit.cover,
                      height: 80,
                      width: 80,
                    ),
                  ),
                ),
              ),
            ),
            Positioned(
              top: 0,
              right: 0,
              child: InkWell(
                onTap: () {
                  FocusManager.instance.primaryFocus?.unfocus();
                  imageList.removeAt(index);
                  setState(() {});
                },
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    color: Colors.white,
                  ),
                  child: const FaIcon(
                    FontAwesomeIcons.solidTimesCircle,
                    color: Colors.red,
                    size: 20,
                  ),
                ),
              ),
            )
          ],
        ),
      );

  _buildFinishButton() {
    return ElevatedButton(
      style: ButtonStyle(
        backgroundColor: MaterialStateProperty.all(Colors.green),
        shape: MaterialStateProperty.all(
          const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(
              Radius.circular(14),
            ),
          ),
        ),
      ),
      onPressed: () async {
        bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
        LocationPermission permission = await Geolocator.checkPermission();
        FocusManager.instance.primaryFocus?.unfocus();
        isValueNotExist = false;
        //
        print(extensionMap.toString());
        print(choiceValue);
        print(subChoiceValue);

        if (!hasSubChoice) {
          subChoiceValue ??= choiceValue;
        }

        locationData ??= await ApplicationUtil.getLocation();
        if (!serviceEnabled ||
            permission == LocationPermission.denied ||
            permission == LocationPermission.deniedForever) {
          ApplicationUtil.showWarningAlertDialog(
            context,
            title: AppLocalizations.of(context)!.locationPermissionRequired,
            desc: kIsWeb
                ? AppLocalizations.of(context)!
                    .appRequiresLocationPermissionforweb
                : AppLocalizations.of(context)!.appRequiresLocationPermission,
            negativeLabel: AppLocalizations.of(context)!.okay,
          );
        } else {
          LocationAccuracyStatus status =
              await Geolocator.getLocationAccuracy();
          if (status == LocationAccuracyStatus.reduced) {
            setState(() {
              fetchingLocation = false;
            });
            ApplicationUtil.showWarningAlertDialog(
              context,
              title: AppLocalizations.of(context)!.locationPermissionRequired,
              desc:
                  "To provide accurate results based on your current position, we need access to your precise location. Please enable Precise Location in your device settings",
              negativeLabel: AppLocalizations.of(context)!.okay,
            );
            return;
          }
          if (locationData == null) {
            if (status == LocationAccuracyStatus.precise) {
              setState(() {
                fetchingLocation = true;
              });
              locationData = await ApplicationUtil.getLocation();
            }
            /*  Container(
              height: 300,
              child: ApplicationUtil.showWarningAlertDialog(
                context,
                title: "Please Wait fetching location Data",
                desc: "",
                negativeLabel: AppLocalizations.of(context)!.okay,
              ),
            );*/
            return;
          } else {
            if (getIt<ServiceType>().type == ServiceRequestType.ADD) {
              if (_validateForm()) {
                //VALIDATE EXTENTION FIELD
                dynamic response = validateExtensionMap(extensionMap);
                if (response is bool) {
                  return;
                }

                List<Map<String, dynamic>> base64MapList = [];
                for (var element in imageList) {
                  var imageMap = {
                    "DOCUMENT_TYPE": "image/jpeg",
                    "DOCUMENT_BLOB": element
                  };
                  base64MapList.add(imageMap);
                }
                if (base64MapList.isEmpty) {
                  ApplicationUtil.showMissingPhotosWarningDialog(
                    context,
                    message: AppLocalizations.of(context)!
                        .areYouSureYouWantToCreateService,
                    onSubmit: () {
                      _saveNewServiceRequestInDatabase(base64MapList);
                    },
                    onCancel: () {},
                  );
                } else {
                  _saveNewServiceRequestInDatabase(base64MapList);
                }
              }
            } else if (getIt<ServiceType>().type ==
                ServiceRequestType.NOTIFICATION) {
              debugPrint(
                  "From Complete Button\nChoice value: $choiceValue\nSub-Choice value:$subChoiceValue");
              if (_validateForm()) {
                //VALIDATE EXTENTION FIELD
                dynamic response = validateExtensionMap(extensionMap);
                if (response is bool) {
                  return;
                }

                List<Map<String, dynamic>> base64MapList = [];
                for (var element in imageList) {
                  var imageMap = {
                    "DOCUMENT_TYPE": "image/jpeg",
                    "DOCUMENT_BLOB": element
                  };
                  base64MapList.add(imageMap);
                }
                List<PartItemResponse?> partList =
                    BlocProvider.of<SelectedPartItemBloc>(context)
                        .selectedPartList
                        .where((element) => element!.count > 0)
                        .toList();
                var jsonParts = partList
                    .map((part) => PartItemResponse(
                            partId: part!.bomPartId ?? part.partId,
                            customerId: part.customerId,
                            name: part.name,
                            description: part.description,
                            manufacturer: part.manufacturer,
                            manufacturerPartNo: part.manufacturerPartNo,
                            partType: part.partType,
                            count: part.count)
                        .toMap())
                    .toList();
                if (base64MapList.isEmpty) {
                  ApplicationUtil.showMissingPhotosWarningDialog(
                    context,
                    message: AppLocalizations.of(context)!
                        .areYouSureYouWantToCreateNotification,
                    onSubmit: () {
                      _saveNewNotificationInDatabase(base64MapList, jsonParts);
                    },
                    onCancel: () {},
                  );
                } else {
                  _saveNewNotificationInDatabase(base64MapList, jsonParts);
                }
              }
            } else if (getIt<ServiceType>().type ==
                ServiceRequestType.MOVE_EQUIPMENT) {
              if (_descriptionTextController.text.isEmpty) {
                ApplicationUtil.showSnackBar(
                    context: context,
                    message:
                        AppLocalizations.of(context)!.descriptionCantBeEmpty);
                return;
              }
              dynamic response = validateExtensionMap(extensionMap);
              if (response is bool) {
                return;
              }
              List<Map<String, dynamic>> base64MapList = [];
              for (var element in imageList) {
                var imageMap = {
                  "DOCUMENT_TYPE": "image/jpeg",
                  "DOCUMENT_BLOB": element
                };
                base64MapList.add(imageMap);
              }
              if (base64MapList.isEmpty) {
                ApplicationUtil.showMissingPhotosWarningDialog(
                  context,
                  message: AppLocalizations.of(context)!
                      .areYouSureYouWantToCreateService,
                  onSubmit: () {
                    _saveEquipmentMovementRequestInDatabase(base64MapList);
                  },
                  onCancel: () {},
                );
              } else {
                _saveEquipmentMovementRequestInDatabase(base64MapList);
              }
            }
          }
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 0),
        child: fetchingLocation
            ? const Center(
                child: CircularProgressIndicator(),
              )
            : Text(
                AppLocalizations.of(context)!.complete,
                style: const TextStyle(color: Colors.white, fontSize: 18),
              ),
      ),
    );
  }

  _buildProgressButton() {
    return Center(
      child: Container(
        width: 30,
        height: 30,
        margin: const EdgeInsets.only(left: 40),
        child: const CircularProgressIndicator(),
      ),
    );
  }

  void _onImageButtonPressed(ImageSource source,
      {required BuildContext context, bool isMultiImage = false}) async {
    try {
      final pickedFile = await _picker.pickImage(
        source: source,
        imageQuality: 70,
        maxHeight: 480,
        maxWidth: 640,
      );
      if (kIsWeb) {
        final http.Response responseData =
            await http.get(Uri.parse(pickedFile!.path));
        var uint8list = responseData.bodyBytes;
        base64Img = base64Encode(uint8list);
      } else {
        var uint8list = File(pickedFile!.path).readAsBytesSync();
        base64Img = base64Encode(uint8list);
      }
      //NAVIGATE TO EDIT
      pickedImage = await Navigator.pushNamed(
          context, EditImageHomePage.routeName,
          arguments: base64Img) as String;
      inputFocusNode.unfocus();
      if (pickedImage != null) {
        imageList.add(pickedImage);
        pickedImage = "";
        isValueNotExist = false;
        setState(() {});
      }
    } catch (e) {
      Logger.e(e.toString());
      setState(() {
        isValueNotExist = false;
      });
    }
  }

  _getOtherForm(BuildContext context) => Container(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            RichText(
              textAlign: TextAlign.start,
              text: TextSpan(
                  style: const TextStyle(fontSize: 18),
                  children: <TextSpan>[
                    const TextSpan(
                      text: "* ",
                      style: TextStyle(
                          color: AppColor.redColor,
                          fontWeight: FontWeight.bold),
                    ),
                    TextSpan(
                      text: AppLocalizations.of(context)!.enterDescription,
                      style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontSize: 18,
                          fontWeight: FontWeight.bold),
                    ),
                  ]),
            ),
            const SizedBox(
              height: 5,
            ),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: AppColor.greyBorderColor),
              ),
              constraints: const BoxConstraints(minHeight: 120),
              child: TextFormField(
                focusNode: inputFocusNode,
                //autofocus: false,
                controller: _descriptionTextController,
                textInputAction: TextInputAction.done,
                keyboardType: TextInputType.multiline,
                style: const TextStyle(fontSize: 18, height: 1.2),
                decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.all(10),
                    hintStyle: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 17,
                    ),
                    hintText: getIt<ServiceType>().type ==
                            ServiceRequestType.MOVE_EQUIPMENT
                        ? AppLocalizations.of(context)!.egSeatIsMoved
                        : AppLocalizations.of(context)!.egBrokenChair),
                maxLines: 6,
              ),
            ),
            getIt<ServiceType>().type == ServiceRequestType.NOTIFICATION
                ? _getPartUsedBox()
                : Container(),
            _generateExtensionUI(),
            const SizedBox(
              height: 20,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                RichText(
                  textAlign: TextAlign.start,
                  text: TextSpan(
                      style: const TextStyle(fontSize: 18),
                      children: <TextSpan>[
                        TextSpan(
                          text: AppLocalizations.of(context)!.addPhotos,
                          style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor),
                        ),
                      ]),
                ),
                TextButton(
                  style: ButtonStyle(
                    shape: MaterialStateProperty.all(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15.0),
                        side: BorderSide(color: Theme.of(context).primaryColor),
                      ),
                    ),
                    padding: MaterialStateProperty.all(
                      const EdgeInsets.symmetric(horizontal: 10),
                    ),
                  ),
                  onPressed: () {
                    FocusManager.instance.primaryFocus?.unfocus();
                    _onImageButtonPressed(ImageSource.gallery,
                        context: context);
                  },
                  child: Text(
                    AppLocalizations.of(context)!.browse,
                    style: const TextStyle(
                      fontSize: 18,
                    ),
                  ),
                )
              ],
            ),
            const SizedBox(
              height: 10,
            ),
            _getImages(),
            const SizedBox(
              height: 23,
            ),
            _getFormsButtons(),
          ],
        ),
      );

  _confirmLocationButton() => !isLocationConfirmed
      ? SizedBox(
          width: 140,
          child: ElevatedButton(
            style: ButtonStyle(
              padding: MaterialStateProperty.all(
                const EdgeInsets.symmetric(vertical: 14, horizontal: 20),
              ),
              backgroundColor:
                  MaterialStateProperty.all(AppColor.greenSentColor),
              shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15.0),
                ),
              ),
            ),
            onPressed: () {
              if (selectedLocationId.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        AppLocalizations.of(context)!.pleaseSelectLocation),
                  ),
                );
              } else {
                setState(() {
                  isLocationConfirmed = true;
                  isLocationFreezed = true;
                });
              }
            },
            child: Text(
              AppLocalizations.of(context)!.confirm,
              style: const TextStyle(color: Colors.white, fontSize: 16),
            ),
          ),
        )
      : Container();

  _getChangeLocationButton() => !isLocationConfirmed
      ? SizedBox(
          width: 140,
          child: ElevatedButton(
            style: ButtonStyle(
              padding: MaterialStateProperty.all(
                const EdgeInsets.symmetric(vertical: 14, horizontal: 20),
              ),
              backgroundColor:
                  MaterialStateProperty.all(Theme.of(context).primaryColor),
              shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15.0),
                ),
              ),
            ),
            onPressed: () {
              //changeTerminalControlDisabled = false;
              setState(() {
                isLocationFreezed = false;
              });
              locationCubit.getLocationData();
            },
            child: Text(
              AppLocalizations.of(context)!.change,
              style: const TextStyle(color: Colors.white, fontSize: 16),
            ),
          ),
        )
      : Container();

  _getClusterDetail() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          barcodeResponse!.equipment!.name!,
          style: const TextStyle(
              color: AppColor.greyTextColor,
              fontSize: 18,
              fontWeight: FontWeight.bold),
        ),
        Text(
          barcodeResponse!.equipment!.categoryName!,
          style: const TextStyle(
            fontSize: 12,
            color: AppColor.greyTextColor,
          ),
        ),
      ],
    );
  }

  _generateExtensionUI() {
    Widget widget = ListView.builder(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      physics: const ScrollPhysics(),
      itemCount: extensionList.length,
      itemBuilder: (context, index) {
        Extension extension = extensionList[index];
        return _buildWidgetFromExtension(
            extension.title!, extension.extensionContent!);
      },
    );
    //isValueNotExist = false;
    return widget;
  }

  Widget _buildWidgetFromExtension(
      String title, List<ExtensionContent> extensionContent) {
    return Container(
      padding: const EdgeInsets.all(10),
      margin: const EdgeInsets.only(top: 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Theme.of(context).primaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _getSectionHeader(title),
          _getSectionBody(extensionContent, title),
        ],
      ),
    );
  }

  _getSectionBody(List<ExtensionContent> extensionContentList, String title) {
    if (extensionMap[title] == null) {
      extensionMap[title] = [];
    }
    for (var element in extensionContentList) {}
    return ListView.builder(
      shrinkWrap: true,
      physics: const ScrollPhysics(),
      itemCount: extensionContentList.length,
      itemBuilder: (context, index) {
        ExtensionContent extensionContent = extensionContentList[index];
        if (isValueNotExist) {
          extensionMap[title].add({
            extensionContent.fieldTechName: '',
            'FIELD_MANDATORY': extensionContent.fieldMandatory,
            'FIELD_NAME': extensionContent.fieldName,
            'FIELD_TECH_NAME': extensionContent.fieldTechName,
          });
        }
        if (extensionContent.fieldControl == "TEXT") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                margin: const EdgeInsets.only(
                  top: 5,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: AppColor.greyBorderColor),
                ),
                child: TextField(
                  style: const TextStyle(height: 1.2),
                  decoration: const InputDecoration(
                    contentPadding: EdgeInsets.symmetric(horizontal: 5),
                    border: InputBorder.none,
                    //hintText: extensionContent.fieldName,
                  ),
                  onChanged: (value) {
                    extensionMap[title]
                        [index] = {extensionContent.fieldTechName: value};
                    print(extensionMap.toString());
                  },
                ),
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          );
        } else if (extensionContent.fieldControl == "TEXTAREA") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                margin: const EdgeInsets.symmetric(
                  vertical: 5,
                ),
                padding: const EdgeInsets.symmetric(vertical: 5),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: AppColor.greyBorderColor),
                ),
                child: TextField(
                  minLines: 3,
                  maxLines: 5,
                  style: const TextStyle(height: 1.2),
                  decoration: const InputDecoration(
                    contentPadding: EdgeInsets.symmetric(horizontal: 5),
                    border: InputBorder.none,
                    //  hintText: extensionContent.fieldName,
                  ),
                  onChanged: (value) {
                    extensionMap[title]
                        [index] = {extensionContent.fieldTechName: value};
                    print(extensionMap.toString());
                  },
                ),
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          );
        } else if (extensionContent.fieldControl == "NUMBER") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                margin: const EdgeInsets.symmetric(
                  vertical: 5,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: AppColor.greyBorderColor),
                ),
                child: TextField(
                  style: const TextStyle(height: 1.2),
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(horizontal: 5),
                    border: InputBorder.none,
                    hintText: extensionContent.fieldName,
                  ),
                  onChanged: (value) {
                    extensionMap[title]
                        [index] = {extensionContent.fieldTechName: value};
                    print(extensionMap.toString());
                  },
                ),
              ),
            ],
          );
        } else if (extensionContent.fieldControl == "TOGGLE") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                  margin: const EdgeInsets.symmetric(
                    vertical: 5,
                  ),
                  padding: const EdgeInsets.symmetric(
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: AppColor.greyBorderColor),
                  ),
                  child: Row(
                    children: [
                      Switch(
                        value: _getSwitchValue(
                            extensionMap, title, index, extensionContent),
                        onChanged: (bool value) {
                          extensionMap[title][index] = {
                            extensionContent.fieldTechName: value.toString()
                          };
                          isValueNotExist = false;
                          setState(() {});
                        },
                      ),
                    ],
                  )),
            ],
          );
        } else if (extensionContent.fieldControl == "CHOICE") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              dropDownOption(extensionContent, index, title)
            ],
          );
        } else {
          return Container();
        }
      },
    );
  }

  _getSectionHeader(String title) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontSize: 18,
                fontWeight: FontWeight.bold),
          ),
          const SizedBox(
            height: 10,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 5),
            child: const DottedDivider(
              color: AppColor.redColor,
            ),
          ),
          const SizedBox(
            height: 10,
          ),
        ],
      );

  dropDownOption(
    ExtensionContent extensionContent,
    int index,
    String title,
  ) {
    List<DropDownChoice> dropDownList = [];
    Map<String, dynamic>? data = getIt<Customization>().customizationData;
    if (data != null && data.containsKey("CHOICE")) {
      if (data['CHOICE'].containsKey(extensionContent.fieldChoiceType)) {
        List<dynamic> dropDownOptionList =
            data['CHOICE'][extensionContent.fieldChoiceType];
        dropDownList
            .add(DropDownChoice(choiceName: 'Select', choiceValue: 'select'));
        for (var choiceMap in dropDownOptionList) {
          dropDownList.add(DropDownChoice.fromMap(choiceMap));
        }
      }
    }

    return Container(
      margin: const EdgeInsets.only(top: 5),
      height: 45,
      decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(15)),
      child: DropdownButton(
          underline: Container(),
          value: _getExtensionDropdownSelectedValue(
              dropDownList, title, index, extensionContent),
          hint: Center(
            child: Text(
              _getExtensionDropdownSelectedName(
                  dropDownList, title, index, extensionContent),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          icon: const Icon(
            // Add this
            Icons.arrow_drop_down,
            color: Colors.white,
            size: 40, // Add this
          ),
          isExpanded: true,
          items: dropDownList.map(
            (val) {
              return DropdownMenuItem(
                value: val.choiceValue,
                child: Text(
                  val.choiceName as String,
                  style: const TextStyle(color: Colors.black),
                ),
              );
            },
          ).toList(),
          selectedItemBuilder: (BuildContext ctxt) {
            return dropDownList.map<Widget>((DropDownChoice item) {
              return DropdownMenuItem(
                  child: Container(
                    margin: const EdgeInsets.only(left: 20),
                    child: Text("${item.choiceName}",
                        style: const TextStyle(
                            fontWeight: FontWeight.bold, color: Colors.white)),
                  ),
                  value: item);
            }).toList();
          },
          onChanged: (value) {
            extensionMap[title]
                [index] = {extensionContent.fieldTechName: value};
            print(extensionMap.toString());
            isValueNotExist = false;
            setState(() {});
          }),
    );
  }

  _getLocationDropDown() {
    if (isFirstTime) {
      locationCubit.getLocationData();
      print("_getLocationDropDown");
      isFirstTime = false;
    }
    return BlocConsumer<LocationCubit, LocationState>(
      listener: (context, state) {
        if (state is FetchLocationDataError) {
          if (state.errorMessage == ApiResponse.INVALID_AUTH) {
            Navigator.pushNamedAndRemoveUntil(
                context, LoginPage.routeName, (route) => false,
                arguments: true);
          } else {
            ApplicationUtil.showSnackBar(
                context: context, message: state.errorMessage);
          }
        }
        if (state is FetchedLocationData) {
          locationList = state.locationList;
          //GATE INIT FOR EQUIPMENT MOVEMENT
          /* if (getIt<ServiceType>().type == ServiceRequestType.MOVE_EQUIPMENT) {
            gatesList = [];
            gatesList.add(selectedGate);
            for (var element in dataList) {
              if (selectedTerminal.name == element.name) {
                for (var element in element.gate!) {
                  if (gatesList.indexWhere(
                          (temp) => temp.locationId == element.locationId) <
                      0) {
                    gatesList.add(LocationChoice(
                        name: element.name!, locationId: element.locationId!));
                  }
                }
              }
            }
          }*/
        }
      },
      builder: (context, state) {
        if (state is FetchedLocationData) {
          return locationList.isNotEmpty
              ? LocationDropdownWidget(
                  locationList: locationList,
                  defaultLocationId: selectedLocationId,
                  disableDropDown: isLocationFreezed,
                  onLocationSelected: (value, name, category) {
                    selectedLocationId = value;
                    endLocationName = name;
                    endLocationCategory = category;
                  },
                )
              : _getDefaultLocationLabel();
        } else {
          return _getDefaultLocationLabel();
        }
      },
    );
  }

  bool _validateForm() {
    if (choiceValue == null) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(AppLocalizations.of(context)!.selectAtleastOneTask)));
      return false;
    }
    if (choiceValue == 'OTHER' && subChoiceValue == null) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content:
              Text(AppLocalizations.of(context)!.selectAtleastOneSubTask)));
      return false;
    }

    if (_descriptionTextController.text.isEmpty) {
      ApplicationUtil.showSnackBar(
          context: context,
          message: AppLocalizations.of(context)!.descriptionCantBeEmpty);
      return false;
    }
    /*  if (imageList.isEmpty) {
      ApplicationUtil.showSnackBar(
          context: context, message: "At least one image is required");
      return false;
    }*/
    return true;
  }

  _getSwitchValue(Map<String, dynamic> extensionMap, String title, int index,
      ExtensionContent extensionContent) {
    if (extensionMap[title][index][extensionContent.fieldTechName] == null ||
        extensionMap[title][index][extensionContent.fieldTechName].isEmpty) {
      extensionMap[title][index][extensionContent.fieldTechName] = 'false';
    }
    return extensionMap[title][index][extensionContent.fieldTechName] == 'true';
  }

  _getExtensionDropdownSelectedValue(List<DropDownChoice> dropDownList,
      String title, int index, ExtensionContent extensionContent) {
    String selected =
        extensionMap[title][index][extensionContent.fieldTechName];
    if (selected.isNotEmpty) return selected;
    return dropDownList[0].choiceValue;
  }

  String _getExtensionDropdownSelectedName(List<DropDownChoice> dropDownList,
      String title, int index, ExtensionContent extensionContent) {
    var selected = extensionMap[title][index][extensionContent.fieldTechName];
    print(selected);

    for (DropDownChoice choice in dropDownList) {
      if (choice.choiceValue == selected) {
        print('element.choiceName' + choice.choiceName!);
        return choice.choiceName!;
      }
    }
    return dropDownList[0].choiceName!;
  }

  void sendDataToServer() {
    serviceRequestBloc.add(SendPendingServiceRequestToServer());
    //
    print('sendDataToServer');
  }

  setTaskListData() {
    List<Choice> choiceDataList = [];
    Map<String, dynamic>? data = getIt<Customization>().customizationData;
    if (data != null) {
      if (data.containsKey("CHOICE")) {
        if (data['CHOICE'].containsKey('TASK_TYPES')) {
          List<dynamic> serviceRequestChoiceDataList =
              data['CHOICE']['TASK_TYPES'];
          for (var choiceMap in serviceRequestChoiceDataList) {
            Choice choice = Choice(
                choiceValue: choiceMap['CHOICE_VALUE'],
                choiceName: choiceMap['CHOICE_NAME']);
            List<SubChoice> subChoiceList = [];
            if (choiceMap.containsKey('SUB_CHOICE')) {
              List<dynamic> serviceRequestSubChoiceDataList =
                  choiceMap['SUB_CHOICE'];
              for (var subChoiceMap in serviceRequestSubChoiceDataList) {
                SubChoice subChoice = SubChoice(
                    choiceName: subChoiceMap['CHOICE_NAME'],
                    choiceValue: subChoiceMap['CHOICE_VALUE']);
                subChoiceList.add(subChoice);
              }
              doesChoiceHasSubChoice.add(true);
            } else {
              doesChoiceHasSubChoice.add(false);
            }
            choice.subChoice = subChoiceList;
            choiceDataList.add(choice);
          }
        }
      }
    }
    debugPrint('doesChoiceHasSubChoice: ${doesChoiceHasSubChoice.toString()}');
    return choiceDataList;
  }

  getExtensionData() {
    List<Extension> extensionList = [];
    Map<String, dynamic>? data = getIt<Customization>().customizationData;
    if (data != null) {
      if (getIt<ServiceType>().type == ServiceRequestType.ADD) {
        if (data.containsKey("EXTENSIONS")) {
          if (data['EXTENSIONS'].containsKey('SERVICE_REQUEST')) {
            Map<String, dynamic> sectionMap =
                data['EXTENSIONS']['SERVICE_REQUEST'];
            for (var key in sectionMap.keys) {
              Extension extension = Extension();
              extension.title = key;
              List<dynamic> sectionDetailList = sectionMap[key];
              List<ExtensionContent> extensionContentList = [];
              for (var mapData in sectionDetailList) {
                extensionContentList.add(ExtensionContent.fromMap(mapData));
              }
              extension.extensionContent = extensionContentList;
              extensionList.add(extension);
            }
          }
        }
      } else if (getIt<ServiceType>().type ==
          ServiceRequestType.MOVE_EQUIPMENT) {
        apiBloc.add(FetchTerminalData(
            locationId: barcodeResponse!.equipment!.locationId!));
        if (data.containsKey("EXTENSIONS")) {
          if (data['EXTENSIONS'].containsKey('MOVEMENT')) {
            Map<String, dynamic> sectionMap = data['EXTENSIONS']['MOVEMENT'];
            for (var key in sectionMap.keys) {
              Extension extension = Extension();
              extension.title = key;
              List<dynamic> sectionDetailList = sectionMap[key];
              List<ExtensionContent> extensionContentList = [];
              for (var mapData in sectionDetailList) {
                extensionContentList.add(ExtensionContent.fromMap(mapData));
              }
              extension.extensionContent = extensionContentList;
              extensionList.add(extension);
            }
          }
        }
      } else if (getIt<ServiceType>().type == ServiceRequestType.NOTIFICATION) {
        apiBloc.add(FetchTerminalData(
            locationId: barcodeResponse!.equipment!.locationId!));
        if (data.containsKey("EXTENSIONS")) {
          if (data['EXTENSIONS'].containsKey('AUDIT')) {
            Map<String, dynamic> sectionMap = data['EXTENSIONS']['AUDIT'];
            for (var key in sectionMap.keys) {
              Extension extension = Extension();
              extension.title = key;
              List<dynamic> sectionDetailList = sectionMap[key];
              List<ExtensionContent> extensionContentList = [];
              for (var mapData in sectionDetailList) {
                extensionContentList.add(ExtensionContent.fromMap(mapData));
              }
              extension.extensionContent = extensionContentList;
              extensionList.add(extension);
            }
          }
        }
      }
    }
    return extensionList;
  }

  void _clearBlocList() {
    BlocProvider.of<SelectedPartItemBloc>(context)
        .add(UpdatePartItem(isReset: true));
  }

  void _initBarCodeResponse() {
    barcodeResponse = getIt<BarcodeResponse>().barcodeResponseInstance;
    if (barcodeResponse!.equipment != null) {
      selectedLocationId = barcodeResponse!.equipment!.locationId!;
      //location for label dropdown
      selectedLocation = barcodeResponse!.equipment!.location!;
      //set end location and categoryName
      barcodeResponse!.equipment!.location!.forEach((element) {
        element.forEach((root, value) {
          if (element /*[root]*/ ['LOCATION_ID'] == selectedLocationId) {
            endLocationName = element /*[root]*/ ['NAME'];
            endLocationCategory = element['CATEGORY'];
          }
        });
      });
    }
    taskList = setTaskListData();
    extensionList = getExtensionData();
    print(extensionList.length);
    _descriptionTextController = TextEditingController();

/*    if (widget.serviceRequestData!.type == ServiceRequestType.ADD) {
      _descriptionTextController = TextEditingController();
    } else {
      _descriptionTextController =
          TextEditingController(text: barcodeResponse!.equipment!.description);
    }*/
  }

  dynamic validateExtensionMap(Map<String?, dynamic> extensionMap) {
    List<String> errorList = [];
    List<Map<String?, dynamic>> returnedMap = [];
    extensionMap.forEach((key, value) {
      List<dynamic> innerMap = extensionMap[key];
      for (var element in innerMap) {
        Map<String?, dynamic> map = element;
        if (map['FIELD_MANDATORY'] == 'Y' &&
            map[map['FIELD_TECH_NAME']].isEmpty) {
          errorList.add(map['FIELD_NAME']);
        } else {
          returnedMap.add(element);
        }
      }
    });
    if (errorList.isNotEmpty) {
      ApplicationUtil.showSnackBar(
          context: context, message: '${errorList[0]} is required');
      return true;
    }
    return returnedMap;
  }

  Widget _getDefaultLocationLabel() => LocationLabel(
        locationMap: selectedLocation,
      );

  _getLabelBaseOnMandatory(ExtensionContent extensionContent) {
    return RichText(
      textAlign: TextAlign.start,
      text: TextSpan(style: const TextStyle(fontSize: 18), children: <TextSpan>[
        extensionContent.fieldMandatory == "Y"
            ? const TextSpan(
                text: "* ",
                style: TextStyle(
                    color: AppColor.redColor, fontWeight: FontWeight.bold),
              )
            : const TextSpan(text: ''),
        TextSpan(
          text: extensionContent.fieldName,
          style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColor.blackTextColor),
        ),
      ]),
    );
  }

  void showAlertDialog(BuildContext ctx) {
    AlertDialog alert = AlertDialog(
      title: Text(AppLocalizations.of(context)!.unsavedChange),
      content: Text(AppLocalizations.of(context)!.youWillLoseYourData),
      actions: [
        ElevatedButton(
          style: ButtonStyle(
              backgroundColor: MaterialStateProperty.all(AppColor.redColor)),
          child: Text(AppLocalizations.of(context)!.goBack),
          onPressed: () {
            if (getIt<ServiceType>().type == ServiceRequestType.NOTIFICATION) {
              _clearBlocList();
            }
            Navigator.of(ctx).pop();
            Navigator.of(context).pop(false);
          },
        ),
        ElevatedButton(
          child: Text(AppLocalizations.of(context)!.cancel),
          onPressed: () {
            Navigator.of(ctx).pop();
          },
        ),
      ],
    );
    showDialog(
      barrierDismissible: false,
      context: ctx,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  void _saveNewServiceRequestInDatabase(
      List<Map<String, dynamic>> base64mapList) {
    int? userId = getIt<SharedPreferences>().getInt('userId');
    print(base64mapList);

    serviceRequestBloc.add(
      SaveServiceRequest(
        serviceRequestCompanion: ServiceRequestCompanion(
          locationId: moor.Value(barcodeResponse!.equipment!.locationId),
          newLocationId: moor.Value(
              barcodeResponse!.equipment!.locationId == selectedLocationId
                  ? null
                  : selectedLocationId),
          createdAt: moor.Value(DateTime.now()),
          equipmentBarcodeNumber: moor.Value(barcodeResponse!.equipment!.tag),
          equipmentId:
              moor.Value(barcodeResponse!.equipment!.equipmentId.toString()),
          extn: moor.Value(json.encode(extensionMap)),
          choiceId: const moor.Value(1),
          description: moor.Value(_descriptionTextController.text),
          createdBy: moor.Value(userId),
          status: const moor.Value(0),
          customerId: moor.Value(barcodeResponse!.equipment!.customerId),
          requestChoiceType:
              moor.Value(choiceValue == 'OTHER' ? subChoiceValue : choiceValue),
          requestType: const moor.Value('SERVICE_REQUEST'),
          equipmentName: moor.Value(barcodeResponse!.equipment!.name),
          equipmentCategory:
              moor.Value(barcodeResponse!.equipment!.categoryName),
          document: moor.Value(json.encode(base64mapList)),
          latitude: moor.Value(locationData?.latitude),
          longitude: moor.Value(locationData?.longitude),
          safety: moor.Value(isSafetyIssue),
        ),
      ),
    );
  }

  void _saveNewNotificationInDatabase(List<Map<String, dynamic>> base64mapList,
      List<Map<String, dynamic>> jsonParts) {
    int? userId = getIt<SharedPreferences>().getInt('userId');
    print(base64mapList);

    serviceRequestBloc.add(
      SaveServiceRequest(
        serviceRequestCompanion: ServiceRequestCompanion(
          locationId: moor.Value(barcodeResponse!.equipment!.locationId),
          newLocationId: moor.Value(
              barcodeResponse!.equipment!.locationId == selectedLocationId
                  ? null
                  : selectedLocationId),
          createdAt: moor.Value(DateTime.now()),
          equipmentBarcodeNumber: moor.Value(barcodeResponse!.equipment!.tag),
          equipmentId:
              moor.Value(barcodeResponse!.equipment!.equipmentId.toString()),
          extn: moor.Value(json.encode(extensionMap)),
          choiceId: const moor.Value(1),
          description: moor.Value(_descriptionTextController.text),
          createdBy: moor.Value(userId),
          status: const moor.Value(0),
          //customerId: moor.Value(barcodeResponse!.equipment!.customerId),
          requestChoiceType:
              moor.Value(choiceValue == 'OTHER' ? subChoiceValue : choiceValue),
          requestType: const moor.Value('NOTIFICATION'),
          equipmentName: moor.Value(barcodeResponse!.equipment!.name),
          equipmentCategory:
              moor.Value(barcodeResponse!.equipment!.categoryName),
          parts: moor.Value(json.encode(jsonParts)),
          document: moor.Value(json.encode(base64mapList)),
          latitude: moor.Value(locationData?.latitude),
          longitude: moor.Value(locationData?.longitude),
          auditId: moor.Value(barcodeResponse?.auditId),
        ),
      ),
    );
  }

  _getTaskForms(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            child: RichText(
              textAlign: TextAlign.start,
              text: TextSpan(
                  style: const TextStyle(fontSize: 18),
                  children: <TextSpan>[
                    const TextSpan(
                      text: "* ",
                      style: TextStyle(
                          color: AppColor.redColor,
                          fontWeight: FontWeight.bold),
                    ),
                    TextSpan(
                      text: AppLocalizations.of(context)!.selectTask,
                      style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontSize: 18,
                          fontWeight: FontWeight.bold),
                    ),
                  ]),
            )),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 5),
          child: Column(
            children: taskList.map(
              (Choice data) {
                return Column(
                  children: [
                    SizedBox(
                      height: 40,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Flexible(
                            fit: FlexFit.loose,
                            child: RadioListTile(
                              contentPadding: const EdgeInsets.all(0),
                              title: Transform.translate(
                                offset: const Offset(-16, 0),
                                child: Text(
                                  data.choiceName,
                                  textAlign: TextAlign.start,
                                  style: const TextStyle(
                                      color: AppColor.greyTextColor),
                                ),
                              ),
                              groupValue: choiceValue,
                              value: data.choiceValue,
                              onChanged: (val) {
                                setState(() {
                                  isValueNotExist = false;
                                  radioItem = data;
                                  choiceValue = data.choiceValue;
                                  if (choiceValue != 'OTHER') {
                                    subChoiceValue = null;
                                  }

                                  if (data.subChoice == null) {
                                    hasSubChoice = false;
                                  } else {
                                    hasSubChoice = true;
                                  }

                                  debugPrint(taskList.length.toString());
                                  debugPrint(data.choiceName);
                                  debugPrint(
                                      "From Choice Selection\nChoice value: $choiceValue\nSub-Choice value:$subChoiceValue");
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    AnimatedSize(
                      curve: Curves.linearToEaseOut,
                      duration: const Duration(milliseconds: 400),
                      //margin: EdgeInsets.only(left: 30),
                      //vsync: this,
                      child: (data.subChoice != null &&
                              data.choiceValue == choiceValue)
                          ? Container(
                              margin: const EdgeInsets.only(left: 30),
                              child: Column(
                                children: data.subChoice!
                                    .map((data) => SizedBox(
                                          height: 40,
                                          child: Row(
                                            children: [
                                              Flexible(
                                                fit: FlexFit.loose,
                                                child: RadioListTile(
                                                  title: Transform.translate(
                                                    offset:
                                                        const Offset(-16, 0),
                                                    child: Text(
                                                      data.choiceName,
                                                      style: const TextStyle(
                                                          color: AppColor
                                                              .greyTextColor),
                                                    ),
                                                  ),
                                                  groupValue: subChoiceValue,
                                                  value: data.choiceValue,
                                                  onChanged: (val) {
                                                    setState(() {
                                                      isValueNotExist = false;
                                                      subChoiceValue =
                                                          data.choiceValue;
                                                      debugPrint(
                                                          "From Sub-Choice Selection\nChoice value: $choiceValue\nSub-Choice "
                                                          "value:$subChoiceValue");
                                                    });
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                        ))
                                    .toList(),
                              ),
                            )
                          : Container(),
                    ),
                  ],
                );
              },
            ).toList(),
          ),
        ),
        const SizedBox(
          height: 20,
        ),
      ],
    );
  }

  void _saveEquipmentMovementRequestInDatabase(base64mapList) {
    int? userId = getIt<SharedPreferences>().getInt('userId');
    print(base64mapList);
    ServiceRequestCompanion serviceRequestCompanion = ServiceRequestCompanion(
      locationId: moor.Value(barcodeResponse!.equipment!.locationId),
      newLocationId: moor.Value(selectedLocationId),
      createdAt: moor.Value(DateTime.now()),
      equipmentBarcodeNumber: moor.Value(barcodeResponse!.equipment!.tag),
      equipmentId:
          moor.Value(barcodeResponse!.equipment!.equipmentId.toString()),
      extn: moor.Value(json.encode(extensionMap)),
      description: moor.Value(_descriptionTextController.text),
      createdBy: moor.Value(userId),
      status: const moor.Value(0),
      customerId: moor.Value(barcodeResponse!.equipment!.customerId),
      requestType: const moor.Value('MOVEMENT'),
      equipmentName: moor.Value(barcodeResponse!.equipment!.name),
      equipmentCategory: moor.Value(barcodeResponse!.equipment!.categoryName),
      document: moor.Value(json.encode(base64mapList)),
      latitude: moor.Value(locationData?.latitude),
      longitude: moor.Value(locationData?.longitude),
    );
    print(serviceRequestCompanion.toString());
    serviceRequestBloc.add(
      SaveServiceRequest(serviceRequestCompanion: serviceRequestCompanion),
    );
  }

  _getPartUsedBox() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 15),
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Theme.of(context).primaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BlocBuilder<SelectedPartItemBloc, SelectedPartItemState>(
            builder: (context, state) {
              int partCount = BlocProvider.of<SelectedPartItemBloc>(context)
                  .selectedPartList
                  .fold(0, (sum, part) => (sum) + part!.count);
              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    partCount > 0
                        ? '${AppLocalizations.of(context)!.suggestedParts} ($partCount)'
                        : '${AppLocalizations.of(context)!.suggestedParts}',
                    style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: 20,
                        fontWeight: FontWeight.bold),
                  ),
                  IconButton(
                      onPressed: () {
                        FocusManager.instance.primaryFocus?.unfocus();
                        SelectedPartItemState state1 = state;
                        //String? boId;
                        Navigator.pushNamed(
                          context,
                          SearchItemPartPage.routeName,
                          arguments: SearchItemParam(
                              bomId: barcodeResponse!.equipment!.bomId),
                        );
                      },
                      icon: FaIcon(
                        FontAwesomeIcons.plus,
                        color: Theme.of(context).primaryColor,
                        size: 28,
                      ))
                ],
              );
            },
          ),
          const SizedBox(
            height: 10,
          ),
          const DottedDivider(
            color: AppColor.redColor,
          ),
          const SizedBox(
            height: 10,
          ),
          BlocBuilder<SelectedPartItemBloc, SelectedPartItemState>(
            builder: (context, state) {
              print(state);
              if (state is UpdatedPartItem) {
                return ListView.separated(
                  shrinkWrap: true,
                  physics: const ScrollPhysics(),
                  itemCount: state.partItemsList.length,
                  itemBuilder: (context, index) {
                    PartItemResponse part = state.partItemsList[index]!;
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Flexible(
                                    child: Text(
                                      part.name,
                                      //overflow: TextOverflow.ellipsis,
                                      style: const TextStyle(fontSize: 18),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(
                                height: 3,
                              ),
                              Text(
                                part.manufacturerPartNo,
                                style: const TextStyle(fontSize: 16),
                              ),
                            ],
                          ),
                        ),
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Row(
                              children: [
                                Container(
                                  //height: 35,
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        color: AppColor.greyBorderColor),
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                  child: BlocBuilder<RepairButtonCubit,
                                      RepairButtonState>(
                                    builder: (context, state) {
                                      return Column(
                                        children: [
                                          Row(
                                            children: [
                                              Container(
                                                width: 30,
                                                //height: 35,
                                                decoration: const BoxDecoration(
                                                  border: Border(
                                                    right: BorderSide(
                                                        color: AppColor
                                                            .greyBorderColor),
                                                  ),
                                                ),
                                                child: IconButton(
                                                  onPressed: state
                                                          is HidePartUsedButtonButton
                                                      ? null
                                                      : () {
                                                          if (part.count > 0) {
                                                            part.count -= 1;
                                                            setState(() {});
                                                          }
                                                        },
                                                  icon: FaIcon(
                                                    FontAwesomeIcons.minus,
                                                    color: state
                                                            is HidePartUsedButtonButton
                                                        ? AppColor
                                                            .greyBorderColor
                                                        : Theme.of(context)
                                                            .primaryColor,
                                                    size: 20,
                                                  ),
                                                ),
                                              ),
                                              Container(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 10),
                                                  child: Text(
                                                    '${part.count}',
                                                    style: const TextStyle(
                                                        fontSize: 16,
                                                        height: 1.2),
                                                  )),
                                              Container(
                                                width: 30,
                                                //height: 30,
                                                decoration: const BoxDecoration(
                                                  border: Border(
                                                    left: BorderSide(
                                                        color: AppColor
                                                            .greyBorderColor),
                                                  ),
                                                ),
                                                child: IconButton(
                                                  onPressed: state
                                                          is HidePartUsedButtonButton
                                                      ? null
                                                      : () {
                                                          part.count += 1;
                                                          setState(() {});
                                                        },
                                                  icon: FaIcon(
                                                    FontAwesomeIcons.plus,
                                                    color: state
                                                            is HidePartUsedButtonButton
                                                        ? AppColor
                                                            .greyBorderColor
                                                        : Theme.of(context)
                                                            .primaryColor,
                                                    size: 20,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 3),
                            Text(
                              part.uom != null ? part.uom! : 'null',
                              style: const TextStyle(fontSize: 16),
                            ),
                          ],
                        )
                      ],
                    );
                  },
                  separatorBuilder: (BuildContext context, int index) {
                    return const Divider();
                  },
                );
              }
              return Container();
            },
          ),
          const SizedBox(
            height: 20,
          ),
          /* BlocBuilder<RepairButtonCubit, RepairButtonState>(
            builder: (context, state) {
              print(state);
              if (state is HidePartUsedButtonButton) {
                return Container();
              }
              return SizedBox(
                width: double.infinity,
                child: AppButton.getAcceptGreenButton(
                    onTap: () {
                      BlocProvider.of<RepairButtonCubit>(context)
                          .hidePartUsedConfirmButton();
                    },
                    label: 'Done',
                    color: AppColor.greenSentColor),
              );
            },
          ),*/
          const SizedBox(
            height: 10,
          ),
        ],
      ),
    );
  }

  void _initLocation() {
    ApplicationUtil.getLocation().then((value) {
      setState(() {
        locationData = value;
      });
    });
  }

  _getSelectedLocationDetail() {
    String locationCategory = endLocationCategory;
    if (endLocationCategory == "G") {
      locationCategory = "Gate";
    } else if (endLocationCategory == "T") {
      locationCategory = "Terminal";
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: _getClusterDetail(),
          ),
          const SizedBox(
            width: 20,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                endLocationName,
                style: const TextStyle(
                    color: AppColor.greyTextColor,
                    fontSize: 18,
                    fontWeight: FontWeight.bold),
              ),
              Text(
                locationCategory,
                style: const TextStyle(
                  fontSize: 12,
                  color: AppColor.greyTextColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  _getIsSafetyIssueCheckBox() {
    return Container(
      padding: EdgeInsets.only(),
      alignment: Alignment.center,
      width: double.maxFinite,
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          Image.asset(
            width: 25,
            height: 25,
            "assets/images/hazard.png",
          ),
          const SizedBox(
            width: 10,
          ),
          Text(
            AppLocalizations.of(context)!.markAsSafetyIssue,
            style: const TextStyle(
                fontSize: 18,
                color: AppColor.blackTextColor,
                fontWeight: FontWeight.bold),
          ),
          const Spacer(),
          Checkbox(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(2.0),
              ),
              side: MaterialStateBorderSide.resolveWith(
                (states) => BorderSide(width: 1.5, color: AppColor.redColor),
              ),
              value: isSafetyIssue,
              checkColor: Colors.white,
              activeColor: AppColor.redColor,
              //fillColor: MaterialStateProperty.resolveWith(getColor),
              onChanged: (bool? value) {
                setState(() {
                  isSafetyIssue = value!;
                });
              }),
          const SizedBox(
            width: 10,
          ),
        ],
      ),
    );
  }

  _getSafetyIssueBox() {
    if (getIt<ServiceType>().type == ServiceRequestType.ADD ||
        getIt<ServiceType>().type == ServiceRequestType.DIRECT_REPAIR) {
      return Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
            width: double.infinity,
            child: _getIsSafetyIssueCheckBox(),
          ),
          const SizedBox(
            height: 10,
          ),
        ],
      );
    } else {
      return Container();
    }
  }
}
